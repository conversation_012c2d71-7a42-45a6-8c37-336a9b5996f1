import { useQueryClient } from "@tanstack/react-query"
import { Link as RouterLink } from "@tanstack/react-router"
import { FiBriefcase, FiHome, FiSettings, FiUsers, FiPhoneCall } from "react-icons/fi"
import type { IconType } from "react-icons/lib"

import type { UserPublic } from "@/client"

const items = [
  // { icon: FiHome, title: "Dashboard", path: "/" },
  // { icon: FiBriefcase, title: "Items", path: "/items" },
  { icon: FiPhoneCall, title: "Live Call", path: "/live-call" },
  { icon: FiSettings, title: "User Settings", path: "/settings" },
]

interface SidebarItemsProps {
  onClose?: () => void
  collapsed?: boolean
}

interface Item {
  icon: IconType
  title: string
  path: string
}

const SidebarItems = ({ onClose, collapsed = false }: SidebarItemsProps) => {
  const queryClient = useQueryClient()
  const currentUser = queryClient.getQueryData<UserPublic>(["currentUser"])

  const finalItems: Item[] = currentUser?.is_superuser
    ? [...items, { icon: FiUsers, title: "Admin", path: "/admin" }]
    : items

  const listItems = finalItems.map(({ icon: Icon, title, path }) => (
    <RouterLink key={title} to={path} onClick={onClose}>
      <div
        className={`flex items-center gap-4 px-4 py-2 text-sm hover:bg-muted-foreground/10 rounded transition-all duration-200 ${
          collapsed ? 'justify-center' : ''
        }`}
        title={collapsed ? title : undefined}
      >
        <Icon className="self-center flex-shrink-0" />
        {!collapsed && <span className="ml-2">{title}</span>}
      </div>
    </RouterLink>
  ))

  return (
    <>
      {!collapsed && (
        <p className="text-xs px-4 py-2 font-bold">
          Menu
        </p>
      )}
      <div className="space-y-1">{listItems}</div>
    </>
  )
}

export default SidebarItems
