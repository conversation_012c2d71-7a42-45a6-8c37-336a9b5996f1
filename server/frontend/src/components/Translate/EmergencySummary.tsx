import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Bo<PERSON> } from "lucide-react"

interface EmergencySummaryData {
  location: string
  emergencyType: string
  caller: string
  timeReported: string
  status: string
  keyDetails: string[]
}

interface EmergencySummaryProps {
  summary: EmergencySummaryData
}

export function EmergencySummary({ summary }: EmergencySummaryProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          Emergency Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <div className="space-y-3">
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">LOCATION</h4>
              <p className="text-sm">{summary.location}</p>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">EMERGENCY TYPE</h4>
              <p className="text-sm">{summary.emergencyType}</p>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">CALLER</h4>
              <p className="text-sm">{summary.caller}</p>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">TIME REPORTED</h4>
              <p className="text-sm">{summary.timeReported}</p>
            </div>
            <div>
              <h4 className="font-medium text-sm text-muted-foreground">STATUS</h4>
              <Badge variant="default">{summary.status}</Badge>
            </div>
          </div>
        </div>

        <Separator />

        <div>
          <h4 className="font-medium text-sm text-muted-foreground mb-2">KEY DETAILS</h4>
          <ul className="space-y-1">
            {summary.keyDetails.map((detail, index) => (
              <li key={index} className="text-sm flex items-start gap-2">
                <span className="text-muted-foreground">•</span>
                {detail}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
