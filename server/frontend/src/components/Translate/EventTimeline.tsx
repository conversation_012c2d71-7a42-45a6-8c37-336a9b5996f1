import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { FileText } from "lucide-react"

interface TimelineEvent {
  id: string
  time: string
  type: string
  title: string
  description: string
  icon: string
  status: "completed" | "in_progress" | "pending"
}

interface EventTimelineProps {
  events: TimelineEvent[]
}

export function EventTimeline({ events }: EventTimelineProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Event Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {events.map((event, index) => (
              <div key={event.id} className="flex gap-3">
                <div className="flex flex-col items-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                      event.status === "completed"
                        ? "bg-green-100"
                        : event.status === "in_progress"
                          ? "bg-blue-100"
                          : "bg-gray-100"
                    }`}
                  >
                    {event.icon}
                  </div>
                  {index < events.length - 1 && <div className="w-0.5 h-8 bg-gray-200 mt-2"></div>}
                </div>
                <div className="flex-1 pb-4">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-medium">{event.title}</h4>
                    <span className="text-xs text-muted-foreground">{event.time}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">{event.description}</p>
                  <Badge
                    variant={event.status === "completed" ? "default" : "secondary"}
                    className="text-xs mt-1"
                  >
                    {event.status === "completed"
                      ? "Completed"
                      : event.status === "in_progress"
                        ? "In Progress"
                        : "Pending"}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
