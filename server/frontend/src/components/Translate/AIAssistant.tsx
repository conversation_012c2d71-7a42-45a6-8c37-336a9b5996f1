import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Bot, ChevronRight, Send, FileText } from "lucide-react"

interface Source {
  id: string
  text: string
  speaker: string
  timestamp: string
}

interface ChatMessage {
  id: string
  type: "event" | "ai" | "user"
  eventType?: string
  message: string
  timestamp: string
  confidence?: number
  sources?: Source[]
}

interface AIAssistantProps {
  isOpen: boolean
  onClose: () => void
  messages: ChatMessage[]
  onSourceClick: (sourceId: string) => void
}

export function AIAssistant({ isOpen, onClose, messages, onSourceClick }: AIAssistantProps) {
  const [chatMessage, setChatMessage] = useState("")

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      setChatMessage("")
      // Handle sending message to LLM
    }
  }

  const EventIcon = ({ eventType }: { eventType: string }) => {
    const getEventIcon = (type: string) => {
      switch (type) {
        case "location_detected":
          return "📍"
        case "emergency_type_identified":
          return "🚨"
        case "language_detected":
          return "🌐"
        case "caller_info_updated":
          return "👤"
        case "priority_changed":
          return "⚠️"
        case "services_dispatched":
          return "🚑"
        default:
          return "ℹ️"
      }
    }

    return <span className="text-sm">{getEventIcon(eventType)}</span>
  }

  const ConfidenceIndicator = ({ confidence }: { confidence: number }) => {
    const getConfidenceColor = (conf: number) => {
      if (conf >= 0.9) return "bg-green-500"
      if (conf >= 0.7) return "bg-yellow-500"
      return "bg-red-500"
    }

    const getConfidenceText = (conf: number) => {
      if (conf >= 0.9) return "High"
      if (conf >= 0.7) return "Medium"
      return "Low"
    }

    return (
      <div className="flex items-center gap-1">
        <div className={`h-2 w-2 rounded-full ${getConfidenceColor(confidence)}`}></div>
        <span className="text-xs text-muted-foreground">
          {getConfidenceText(confidence)} ({Math.round(confidence * 100)}%)
        </span>
      </div>
    )
  }

  if (!isOpen) return null

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col max-h-screen">
      <div className="p-4 border-b flex-shrink-0">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold flex items-center gap-2">
            <Bot className="h-4 w-4" />
            AI Assistant
          </h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex-1 min-h-0 p-4">
        <ScrollArea className="h-full">
          <div className="space-y-4 pr-4">
            {messages.map((msg, index) => (
              <div key={index}>
                {msg.type === "event" ? (
                  // AI Event Notification
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                    <div className="flex items-start gap-2">
                      <EventIcon eventType={msg.eventType!} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <Badge variant="outline" className="text-xs bg-amber-100 text-amber-800 border-amber-300">
                            AI Event
                          </Badge>
                          <span className="text-xs text-amber-600">{msg.timestamp}</span>
                        </div>
                        <p className="text-sm text-amber-900 font-medium">{msg.message}</p>
                        {msg.confidence && (
                          <div className="mt-2">
                            <ConfidenceIndicator confidence={msg.confidence} />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  // Regular Chat Messages
                  <div className={`flex ${msg.type === "user" ? "justify-end" : "justify-start"}`}>
                    <div className="max-w-[90%] space-y-2">
                      <div
                        className={`p-3 rounded-lg text-sm ${
                          msg.type === "user" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-900"
                        }`}
                      >
                        <p>{msg.message}</p>
                        <div
                          className={`flex items-center justify-between mt-2 ${
                            msg.type === "user" ? "text-blue-100" : "text-gray-500"
                          }`}
                        >
                          <p className="text-xs">{msg.timestamp}</p>
                          {msg.confidence && <ConfidenceIndicator confidence={msg.confidence} />}
                        </div>
                      </div>

                      {/* Source Citations */}
                      {msg.sources && msg.sources.length > 0 && (
                        <div className="bg-gray-50 rounded-lg p-2 border">
                          <div className="flex items-center gap-1 mb-2">
                            <FileText className="h-3 w-3 text-gray-500" />
                            <span className="text-xs font-medium text-gray-600">
                              Sources ({msg.sources.length})
                            </span>
                          </div>
                          <div className="space-y-1">
                            {msg.sources.map((source, sourceIndex) => (
                              <button
                                key={sourceIndex}
                                onClick={() => onSourceClick(source.id)}
                                className="w-full text-left p-2 rounded bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 transition-colors"
                              >
                                <div className="flex items-center justify-between mb-1">
                                  <Badge
                                    variant={source.speaker === "Caller" ? "secondary" : "default"}
                                    className="text-xs"
                                  >
                                    {source.speaker}
                                  </Badge>
                                  <span className="text-xs text-gray-500">{source.timestamp}</span>
                                </div>
                                <p className="text-xs text-gray-700 truncate">"{source.text}"</p>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      <div className="p-4 border-t flex-shrink-0">
        <div className="flex gap-2">
          <Input
            placeholder="Ask AI about the call..."
            value={chatMessage}
            onChange={(e) => setChatMessage(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
            className="flex-1"
          />
          <Button size="sm" onClick={handleSendMessage}>
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
