import { Card, CardContent } from "@/components/ui/card"

export function StatusBar() {
  return (
    <Card>
      <CardContent className="py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">Translation: Online</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">Audio: Excellent</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
              <span className="text-sm">Latency: 150ms</span>
            </div>
          </div>

          <div className="text-sm text-muted-foreground">OP-2024-001 | TR-{Date.now().toString().slice(-6)}</div>
        </div>
      </CardContent>
    </Card>
  )
}
