import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Globe } from "lucide-react"

interface TranscriptEntry {
  id: string
  speaker: string
  text: string
  original: string
  timestamp: string
}

interface TranslationFeedProps {
  transcript: TranscriptEntry[]
  highlightedMessageId: string | null
}

export function TranslationFeed({ transcript, highlightedMessageId }: TranslationFeedProps) {
  return (
    <Card className="lg:col-span-2">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Translation Feed
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-muted-foreground">Live</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {transcript.map((entry, index) => (
              <div
                key={index}
                className={`space-y-2 transition-all duration-500 ${
                  highlightedMessageId === entry.id
                    ? "bg-blue-50 border-blue-200 border-2 rounded-lg p-2"
                    : ""
                }`}
              >
                <div className="flex items-center gap-2">
                  <Badge variant={entry.speaker === "Caller" ? "secondary" : "default"} className="text-xs">
                    {entry.speaker}
                  </Badge>
                  <span className="text-xs text-muted-foreground">{entry.timestamp}</span>
                  {highlightedMessageId === entry.id && (
                    <Badge variant="outline" className="text-xs bg-blue-100">
                      Referenced by AI
                    </Badge>
                  )}
                </div>

                <div className="p-3 bg-white rounded-lg border">
                  <p className="text-sm font-medium mb-1">{entry.text}</p>
                  <p className="text-xs text-muted-foreground italic">{entry.original}</p>
                </div>

                {index < transcript.length - 1 && <Separator className="my-2" />}
              </div>
            ))}
          </div>
        </ScrollArea>

        {/* Current Input */}
        <div className="mt-4 p-3 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">Listening...</span>
          </div>
          <p className="text-sm text-muted-foreground italic">Waiting for speech input...</p>
        </div>
      </CardContent>
    </Card>
  )
}
