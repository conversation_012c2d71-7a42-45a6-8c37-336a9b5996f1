import { Button } from "@/components/ui/button"
import {
  Mi<PERSON>,
  Mic<PERSON>ff,
  Volume2,
  VolumeX,
  RepeatIcon as Record,
  Square,
  PhoneOff,
} from "lucide-react"

interface CallControlsProps {
  isMuted: boolean
  setIsMuted: (muted: boolean) => void
  speakerOn: boolean
  setSpeakerOn: (on: boolean) => void
  isRecording: boolean
  setIsRecording: (recording: boolean) => void
  isCallActive: boolean
  setIsCallActive: (active: boolean) => void
}

export function CallControls({
  isMuted,
  setIsMuted,
  speakerOn,
  setSpeakerOn,
  isRecording,
  setIsRecording,
  isCallActive,
  setIsCallActive,
}: CallControlsProps) {
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Call Controls</h4>
      <div className="space-y-2">
        <Button
          variant={isMuted ? "destructive" : "outline"}
          size="sm"
          onClick={() => setIsMuted(!isMuted)}
          className="w-full flex items-center gap-2 justify-start"
        >
          {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
          {isMuted ? "Unmute Microphone" : "Mute Microphone"}
        </Button>

        <Button
          variant={speakerOn ? "default" : "outline"}
          size="sm"
          onClick={() => setSpeakerOn(!speakerOn)}
          className="w-full flex items-center gap-2 justify-start"
        >
          {speakerOn ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
          {speakerOn ? "Turn Off Speaker" : "Turn On Speaker"}
        </Button>

        <Button
          variant={isRecording ? "destructive" : "outline"}
          size="sm"
          onClick={() => setIsRecording(!isRecording)}
          className="w-full flex items-center gap-2 justify-start"
        >
          {isRecording ? <Square className="h-4 w-4" /> : <Record className="h-4 w-4" />}
          {isRecording ? "Stop Recording" : "Start Recording"}
        </Button>

        <Button
          variant="destructive"
          size="sm"
          onClick={() => setIsCallActive(false)}
          className="w-full flex items-center gap-2 justify-start"
        >
          <PhoneOff className="h-4 w-4" />
          End Call
        </Button>
      </div>
    </div>
  )
}
