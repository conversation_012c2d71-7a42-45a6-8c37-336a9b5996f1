import * as React from "react"
import { <PERSON>, EyeOff } from "lucide-react"
import { FieldErrors } from "react-hook-form"

import { cn } from "@/lib/utils"
import { Button } from "./button"
import { Input } from "./input"
import { Label } from "./label"

interface PasswordInputProps extends React.ComponentProps<"input"> {
  type: string
  startElement?: React.ReactNode
  errors?: FieldErrors<any>
}

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, type, startElement, errors, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const fieldName = type // Use type as field name for error lookup
    const error = errors?.[fieldName]

    return (
      <div className="space-y-2">
        <Label htmlFor={fieldName} className="capitalize">
          {fieldName.replace(/_/g, " ")}
        </Label>
        <div className="relative">
          {startElement && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              {startElement}
            </div>
          )}
          <Input
            id={fieldName}
            ref={ref}
            type={showPassword ? "text" : "password"}
            className={cn(
              startElement ? "pl-10" : "",
              "pr-10",
              className
            )}
            {...props}
          />
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={() => setShowPassword(!showPassword)}
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        </div>
        {error && (
          <p className="text-sm text-destructive">{error.message}</p>
        )}
      </div>
    )
  }
)

PasswordInput.displayName = "PasswordInput"

export { PasswordInput }
