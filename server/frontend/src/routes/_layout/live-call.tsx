"use client"

import { useState, useEffect } from "react"
import { createFileRoute } from "@tanstack/react-router"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Globe,
  MessageSquare,
  ChevronRight,
  ChevronLeft,
  FileText,
} from "lucide-react"

import { TranslationFeed } from "@/components/Translate/TranslationFeed"
import { CallInformation } from "@/components/Translate/CallInformation"
import { EmergencySummary } from "@/components/Translate/EmergencySummary"
import { EventTimeline } from "@/components/Translate/EventTimeline"
import { AIAssistant } from "@/components/Translate/AIAssistant"
import { StatusBar } from "@/components/Translate/StatusBar"

export const Route = createFileRoute("/_layout/live-call")({
  component: Translate,
})

function Translate() {
  const [isCallActive, setIsCallActive] = useState(true)
  const [isMuted, setIsMuted] = useState(false)
  const [isRecording, setIsRecording] = useState(true)
  const [speakerOn, setSpeakerOn] = useState(true)
  const [callDuration, setCallDuration] = useState(0)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("translation")
  const [highlightedMessageId, setHighlightedMessageId] = useState<string | null>(null)

  // Simulate call duration timer
  useEffect(() => {
    if (isCallActive) {
      const timer = setInterval(() => {
        setCallDuration((prev) => prev + 1)
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [isCallActive])

  const mockChatMessages = [
    {
      id: "event-1",
      type: "event" as const,
      eventType: "location_detected",
      message: "Location detected: 123 Main Street",
      timestamp: "14:32:22",
      confidence: 0.98,
    },
    {
      id: "ai-1",
      type: "ai" as const,
      message: "I'm analyzing the emergency call. The caller needs medical assistance at 123 Main Street.",
      timestamp: "14:32:30",
      sources: [
        { id: "transcript-0", text: "I need urgent medical help", speaker: "Caller", timestamp: "14:32:15" },
        { id: "transcript-2", text: "I'm at 123 Main Street", speaker: "Caller", timestamp: "14:32:22" },
      ],
      confidence: 0.95,
    },
    {
      id: "event-2",
      type: "event" as const,
      eventType: "emergency_type_identified",
      message: "Emergency type identified: Medical Emergency",
      timestamp: "14:32:32",
      confidence: 0.89,
    },
    {
      id: "user-1",
      type: "user" as const,
      message: "What type of medical emergency?",
      timestamp: "14:32:35",
    },
    {
      id: "ai-2",
      type: "ai" as const,
      message:
        "Based on the conversation, it appears to be a general medical emergency. The caller hasn't specified symptoms yet. I'm monitoring for additional details about the nature of the emergency.",
      timestamp: "14:32:40",
      sources: [{ id: "transcript-0", text: "I need urgent medical help", speaker: "Caller", timestamp: "14:32:15" }],
      confidence: 0.78,
    },
    {
      id: "event-3",
      type: "event" as const,
      eventType: "language_detected",
      message: "Language detected: Spanish → English translation active",
      timestamp: "14:32:45",
      confidence: 0.96,
    },
    {
      id: "event-4",
      type: "event" as const,
      eventType: "services_dispatched",
      message: "Emergency services dispatched to confirmed location. Language barrier resolved.",
      timestamp: "14:33:10",
      sources: [
        { id: "transcript-3", text: "Help is on the way", speaker: "Operator", timestamp: "14:32:25" },
        { id: "transcript-2", text: "I'm at 123 Main Street", speaker: "Caller", timestamp: "14:32:22" },
      ],
      confidence: 0.92,
    },
  ]

  const mockTranscript = [
    {
      id: "transcript-0",
      speaker: "Caller",
      text: "I need urgent medical help",
      original: "Necesito ayuda médica urgente",
      timestamp: "14:32:15",
    },
    {
      id: "transcript-1",
      speaker: "Operator",
      text: "What is your location?",
      original: "¿Cuál es su ubicación?",
      timestamp: "14:32:18",
    },
    {
      id: "transcript-2",
      speaker: "Caller",
      text: "I'm at 123 Main Street",
      original: "Estoy en el 123 Main Street",
      timestamp: "14:32:22",
    },
    {
      id: "transcript-3",
      speaker: "Operator",
      text: "Help is on the way",
      original: "La ayuda está en camino",
      timestamp: "14:32:25",
    },
  ]

  const emergencySummary = {
    location: "23 Vivan Street, Mt Eden, AKL",
    emergencyType: "Medical Emergency",
    caller: "Spanish-speaking individual",
    timeReported: "14:32:15",
    status: "Help dispatched",
    keyDetails: [
      "Caller requested urgent medical help",
      "Location confirmed as 123 Main Street",
      "Emergency services notified",
      "Language barrier resolved through translation",
    ],
  }

  const timelineEvents = [
    {
      id: "timeline-1",
      time: "14:32:15",
      type: "call_initiated",
      title: "Emergency Call Received",
      description: "Caller requested urgent medical help",
      icon: "📞",
      status: "completed" as const,
    },
    {
      id: "timeline-2",
      time: "14:32:22",
      type: "location_confirmed",
      title: "Location Confirmed",
      description: "Address verified as 23 Vivan Street, Mt Eden, AKL",
      icon: "📍",
      status: "completed" as const,
    },
    {
      id: "timeline-3",
      time: "14:32:25",
      type: "services_notified",
      title: "Emergency Services Notified",
      description: "Medical emergency dispatch initiated",
      icon: "🚑",
      status: "completed" as const,
    },
    {
      id: "timeline-4",
      time: "14:32:45",
      type: "translation_active",
      title: "Translation System Active",
      description: "Spanish to English translation established",
      icon: "🌐",
      status: "completed" as const,
    },
    {
      id: "timeline-5",
      time: "14:33:10",
      type: "help_dispatched",
      title: "Help Dispatched",
      description: "Emergency responders en route to location",
      icon: "🚨",
      status: "in_progress" as const,
    },
  ]

  const handleSourceClick = (sourceId: string) => {
    setHighlightedMessageId(sourceId)
    setActiveTab("translation")

    // Auto-hide highlight after 3 seconds
    setTimeout(() => {
      setHighlightedMessageId(null)
    }, 3000)
  }

  return (
    <div className="h-[calc(100vh-5rem)] flex">
      <div className="flex-1 p-4">
        <div className="mx-auto max-w-6xl space-y-4">
          {/* Header with Tabs */}
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Live Call</h1>
            <div className="flex items-center gap-4">
              <Badge variant={isCallActive ? "default" : "secondary"} className="text-sm">
                {isCallActive ? "CALL ACTIVE" : "CALL ENDED"}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="flex items-center gap-2"
              >
                <MessageSquare className="h-4 w-4" />
                AI Assistant
                {sidebarOpen ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="translation" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Live Translation
              </TabsTrigger>
              <TabsTrigger value="summary" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                AI Summary
              </TabsTrigger>
            </TabsList>

            <TabsContent value="translation" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <TranslationFeed
                  transcript={mockTranscript}
                  highlightedMessageId={highlightedMessageId}
                />
                <CallInformation
                  callDuration={callDuration}
                  isMuted={isMuted}
                  setIsMuted={setIsMuted}
                  speakerOn={speakerOn}
                  setSpeakerOn={setSpeakerOn}
                  isRecording={isRecording}
                  setIsRecording={setIsRecording}
                  isCallActive={isCallActive}
                  setIsCallActive={setIsCallActive}
                />
              </div>
            </TabsContent>

            <TabsContent value="summary" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <EmergencySummary summary={emergencySummary} />
                <EventTimeline events={timelineEvents} />
              </div>
            </TabsContent>
          </Tabs>

          <StatusBar />
        </div>
      </div>

      <AIAssistant
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        messages={mockChatMessages}
        onSourceClick={handleSourceClick}
      />
    </div>
  )
}
