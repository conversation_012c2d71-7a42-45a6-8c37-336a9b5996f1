{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -p tsconfig.build.json && vite build", "lint": "biome check --apply-unsafe --no-errors-on-unmatched --files-ignore-unknown=true ./", "preview": "vite preview", "generate-client": "openapi-ts"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.28.14", "@tanstack/react-query-devtools": "^5.28.14", "@tanstack/react-router": "1.19.1", "axios": "1.7.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "form-data": "4.0.0", "lucide-react": "^0.535.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.61.1", "react-icons": "^5.4.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.14"}, "devDependencies": {"@biomejs/biome": "1.6.1", "@hey-api/openapi-ts": "^0.57.0", "@playwright/test": "^1.45.2", "@tanstack/router-devtools": "1.19.1", "@tanstack/router-vite-plugin": "1.19.0", "@types/node": "^20.19.9", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "dotenv": "^16.4.5", "postcss": "^8.5.6", "shadcn-ui": "^0.9.5", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.2.2", "vite": "^5.4.14"}}