{"name": "@logivance/translator", "version": "1.0.0", "description": "Electron app to translate emergency calls to save lives.", "homepage": "https://gitlab.com/redmorris/emergency-call-translator#readme", "bugs": {"url": "https://gitlab.com/redmorris/emergency-call-translator/issues"}, "repository": {"type": "git", "url": "git+https://gitlab.com/redmorris/emergency-call-translator.git"}, "license": "MIT", "author": "<PERSON>", "type": "commonjs", "main": "main.js", "scripts": {"start": "electron .", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"electron": "^37.2.5"}}