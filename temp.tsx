
  // Simulate call duration timer
  useEffect(() => {
    if (isCallActive) {
      const timer = setInterval(() => {
        setCallDuration((prev) => prev + 1)
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [isCallActive])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleRequestTranslator = () => {
    setTranslatorRequested(true)
    setTranslatorStatus("requested")
    setAiDisclosurePlayed(true)

    // Simulate translator connection process
    setTimeout(() => setTranslatorStatus("connecting"), 2000)
    setTimeout(() => setTranslatorStatus("connected"), 8000)
  }

  const mockChatMessages = [
    {
      id: "event-1",
      type: "event",
      eventType: "location_detected",
      message: "Location detected: 123 Main Street",
      timestamp: "14:32:22",
      confidence: 0.98,
    },
    {
      id: "ai-1",
      type: "ai",
      message: "I'm analyzing the emergency call. The caller needs medical assistance at 123 Main Street.",
      timestamp: "14:32:30",
      sources: [
        { id: "transcript-0", text: "I need urgent medical help", speaker: "Caller", timestamp: "14:32:15" },
        { id: "transcript-2", text: "I'm at 123 Main Street", speaker: "Caller", timestamp: "14:32:22" },
      ],
      confidence: 0.95,
    },
    {
      id: "event-2",
      type: "event",
      eventType: "emergency_type_identified",
      message: "Emergency type identified: Medical Emergency",
      timestamp: "14:32:32",
      confidence: 0.89,
    },
    {
      id: "user-1",
      type: "user",
      message: "What type of medical emergency?",
      timestamp: "14:32:35",
    },
    {
      id: "ai-2",
      type: "ai",
      message:
        "Based on the conversation, it appears to be a general medical emergency. The caller hasn't specified symptoms yet. I'm monitoring for additional details about the nature of the emergency.",
      timestamp: "14:32:40",
      sources: [{ id: "transcript-0", text: "I need urgent medical help", speaker: "Caller", timestamp: "14:32:15" }],
      confidence: 0.78,
    },
    {
      id: "event-3",
      type: "event",
      eventType: "language_detected",
      message: "Language detected: Spanish → English translation active",
      timestamp: "14:32:45",
      confidence: 0.96,
    },
    {
      id: "event-4",
      type: "event",
      eventType: "services_dispatched",
      message: "Emergency services dispatched to confirmed location. Language barrier resolved.",
      timestamp: "14:33:10",
      sources: [
        { id: "transcript-3", text: "Help is on the way", speaker: "Operator", timestamp: "14:32:25" },
        { id: "transcript-2", text: "I'm at 123 Main Street", speaker: "Caller", timestamp: "14:32:22" },
      ],
      confidence: 0.92,
    },
  ]

  const mockTranscript = [
    {
      id: "transcript-0",
      speaker: "Caller",
      text: "I need urgent medical help",
      original: "Necesito ayuda médica urgente",
      timestamp: "14:32:15",
      type: "conversation",
    },
    {
      id: "transcript-1",
      speaker: "Operator",
      text: "What is your location?",
      original: "¿Cuál es su ubicación?",
      timestamp: "14:32:18",
      type: "conversation",
    },
    {
      id: "transcript-2",
      speaker: "Caller",
      text: "I'm at 123 Main Street",
      original: "Estoy en el 123 Main Street",
      timestamp: "14:32:22",
      type: "conversation",
    },
    {
      id: "transcript-3",
      speaker: "Operator",
      text: "Help is on the way",
      original: "La ayuda está en camino",
      timestamp: "14:32:25",
      type: "conversation",
    },
    ...(aiDisclosurePlayed
      ? [
          {
            id: "disclosure-1",
            speaker: "System",
            text: "Please note: We are currently using AI translation assistance. A human translator is being connected to ensure accuracy.",
            original:
              "Tenga en cuenta: Actualmente estamos usando asistencia de traducción de IA. Se está conectando un traductor humano para garantizar la precisión.",
            timestamp: "14:33:15",
            type: "system_message",
          },
        ]
      : []),
    ...(translatorStatus === "connecting"
      ? [
          {
            id: "connecting-1",
            speaker: "System",
            text: "Connecting human translator...",
            original: "Conectando traductor humano...",
            timestamp: "14:33:17",
            type: "system_message",
          },
        ]
      : []),
    ...(translatorStatus === "connected"
      ? [
          {
            id: "connected-1",
            speaker: "System",
            text: "Human translator Maria Rodriguez has joined the call",
            original: "La traductora humana María Rodríguez se ha unido a la llamada",
            timestamp: "14:33:23",
            type: "system_message",
          },
          {
            id: "translator-1",
            speaker: "Translator",
            text: "Hello, I'm Maria, your translator. I'm here to assist with this emergency call.",
            original: "Hola, soy María, su traductora. Estoy aquí para ayudar con esta llamada de emergencia.",
            timestamp: "14:33:25",
            type: "conversation",
          },
        ]
      : []),
  ]

  const emergencySummary = {
    location: "123 Main Street, New York, NY",
    emergencyType: "Medical Emergency",
    caller: "Spanish-speaking individual",
    timeReported: "14:32:15",
    status: "Help dispatched",
    keyDetails: [
      "Caller requested urgent medical help",
      "Location confirmed as 123 Main Street",
      "Emergency services notified",
      "Language barrier resolved through translation",
      ...(translatorStatus === "connected" ? ["Human translator connected"] : []),
    ],
  }

  const timelineEvents = [
    {
      id: "timeline-1",
      time: "14:32:15",
      type: "call_initiated",
      title: "Emergency Call Received",
      description: "Caller requested urgent medical help",
      icon: "📞",
      status: "completed",
    },
    {
      id: "timeline-2",
      time: "14:32:22",
      type: "location_confirmed",
      title: "Location Confirmed",
      description: "Address verified as 123 Main Street, New York, NY",
      icon: "📍",
      status: "completed",
    },
    {
      id: "timeline-3",
      time: "14:32:25",
      type: "services_notified",
      title: "Emergency Services Notified",
      description: "Medical emergency dispatch initiated",
      icon: "🚑",
      status: "completed",
    },
    {
      id: "timeline-4",
      time: "14:32:45",
      type: "translation_active",
      title: "AI Translation System Active",
      description: "Spanish to English translation established",
      icon: "🤖",
      status: "completed",
    },
    {
      id: "timeline-5",
      time: "14:33:10",
      type: "help_dispatched",
      title: "Help Dispatched",
      description: "Emergency responders en route to location",
      icon: "🚨",
      status: "in_progress",
    },
    ...(translatorRequested
      ? [
          {
            id: "timeline-6",
            time: "14:33:15",
            type: "translator_requested",
            title: "Human Translator Requested",
            description: "AI disclosure played, connecting human translator",
            icon: "👤",
            status: translatorStatus === "connected" ? "completed" : "in_progress",
          },
        ]
      : []),
    ...(translatorStatus === "connected"
      ? [
          {
            id: "timeline-7",
            time: "14:33:23",
            type: "translator_connected",
            title: "Human Translator Connected",
            description: "Maria Rodriguez joined the call",
            icon: "✅",
            status: "completed",
          },
        ]
      : []),
  ]

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      setChatMessage("")
      // Handle sending message to LLM
    }
  }

  const handleSourceClick = (sourceId: string) => {
    setHighlightedMessageId(sourceId)
    setActiveTab("translation")

    // Auto-hide highlight after 3 seconds
    setTimeout(() => {
      setHighlightedMessageId(null)
    }, 3000)
  }

  const EventIcon = ({ eventType }: { eventType: string }) => {
    const getEventIcon = (type: string) => {
      switch (type) {
        case "location_detected":
          return "📍"
        case "emergency_type_identified":
          return "🚨"
        case "language_detected":
          return "🌐"
        case "caller_info_updated":
          return "👤"
        case "priority_changed":
          return "⚠️"
        case "services_dispatched":
          return "🚑"
        default:
          return "ℹ️"
      }
    }

    return <span className="text-sm">{getEventIcon(eventType)}</span>
  }

  const ConfidenceIndicator = ({ confidence }: { confidence: number }) => {
    const getConfidenceColor = (conf: number) => {
      if (conf >= 0.9) return "bg-green-500"
      if (conf >= 0.7) return "bg-yellow-500"
      return "bg-red-500"
    }

    const getConfidenceText = (conf: number) => {
      if (conf >= 0.9) return "High"
      if (conf >= 0.7) return "Medium"
      return "Low"
    }

    return (
      <div className="flex items-center gap-1">
        <div className={`h-2 w-2 rounded-full ${getConfidenceColor(confidence)}`}></div>
        <span className="text-xs text-muted-foreground">
          {getConfidenceText(confidence)} ({Math.round(confidence * 100)}%)
        </span>
      </div>
    )
  }

  const getTranslatorStatusColor = () => {
    switch (translatorStatus) {
      case "requested":
        return "bg-yellow-100 text-yellow-800 border-yellow-300"
      case "connecting":
        return "bg-blue-100 text-blue-800 border-blue-300"
      case "connected":
        return "bg-green-100 text-green-800 border-green-300"
      default:
        return "bg-gray-100 text-gray-800 border-gray-300"
    }
  }

  const getTranslatorStatusText = () => {
    switch (translatorStatus) {
      case "requested":
        return "Translator Requested"
      case "connecting":
        return "Connecting Translator..."
      case "connected":
        return "Translator Connected"
      default:
        return "AI Translation Only"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <div className="flex-1 p-4">
        <div className="mx-auto max-w-6xl space-y-4">
          {/* Header with Tabs */}
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Real-Time Translation Interface</h1>
            <div className="flex items-center gap-4">
              <Badge variant={isCallActive ? "default" : "secondary"} className="text-sm">
                {isCallActive ? "CALL ACTIVE" : "CALL ENDED"}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="flex items-center gap-2"
              >
                <MessageSquare className="h-4 w-4" />
                AI Assistant
                {sidebarOpen ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="translation" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Live Translation
              </TabsTrigger>
              <TabsTrigger value="summary" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                AI Summary
              </TabsTrigger>
            </TabsList>

            <TabsContent value="translation" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Translation Display - now takes 2 columns */}
                <Card className="lg:col-span-2">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Globe className="h-5 w-5" />
                        Translation Feed
                      </CardTitle>
                      <div className="flex items-center gap-4">
                        <Badge className={`text-xs ${getTranslatorStatusColor()}`}>{getTranslatorStatusText()}</Badge>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                          <span className="text-sm text-muted-foreground">Live</span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96">
                      <div className="space-y-4">
                        {mockTranscript.map((entry, index) => (
                          <div
                            key={index}
                            className={`space-y-2 transition-all duration-500 ${
                              highlightedMessageId === entry.id
                                ? "bg-blue-50 border-blue-200 border-2 rounded-lg p-2"
                                : ""
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={
                                  entry.speaker === "Caller"
                                    ? "secondary"
                                    : entry.speaker === "System"
                                      ? "outline"
                                      : entry.speaker === "Translator"
                                        ? "default"
                                        : "default"
                                }
                                className={`text-xs ${
                                  entry.speaker === "System"
                                    ? "bg-purple-100 text-purple-800 border-purple-300"
                                    : entry.speaker === "Translator"
                                      ? "bg-green-100 text-green-800 border-green-300"
                                      : ""
                                }`}
                              >
                                {entry.speaker}
                              </Badge>
                              <span className="text-xs text-muted-foreground">{entry.timestamp}</span>
                              {entry.type === "system_message" && (
                                <Badge variant="outline" className="text-xs bg-purple-50">
                                  System Message
                                </Badge>
                              )}
                              {highlightedMessageId === entry.id && (
                                <Badge variant="outline" className="text-xs bg-blue-100">
                                  Referenced by AI
                                </Badge>
                              )}
                            </div>

                            <div
                              className={`p-3 rounded-lg border ${
                                entry.type === "system_message" ? "bg-purple-50 border-purple-200" : "bg-white"
                              }`}
                            >
                              <p className="text-sm font-medium mb-1">{entry.text}</p>
                              <p className="text-xs text-muted-foreground italic">{entry.original}</p>
                            </div>

                            {index < mockTranscript.length - 1 && <Separator className="my-2" />}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>

                    {/* Current Input */}
                    <div className="mt-4 p-3 border-2 border-dashed border-gray-300 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">Listening...</span>
                      </div>
                      <p className="text-sm text-muted-foreground italic">Waiting for speech input...</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Call Information Panel - now on the right */}
                <Card className="lg:col-span-1">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Phone className="h-4 w-4" />
                      Call Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Caller:</span>
                        <span>+****************</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Location:</span>
                        <span>New York, NY</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Duration:</span>
                        <span>{formatTime(callDuration)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Priority:</span>
                        <Badge variant="destructive" className="text-xs">
                          EMERGENCY
                        </Badge>
                      </div>
                    </div>

                    <Separator />

                    {/* Translation Support */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Translation Support</h4>
                      <div className="space-y-2">
                        <Button
                          variant={translatorRequested ? "default" : "outline"}
                          size="sm"
                          onClick={handleRequestTranslator}
                          disabled={translatorRequested}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          {translatorStatus === "connected" ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : translatorStatus === "connecting" ? (
                            <Clock className="h-4 w-4 animate-spin" />
                          ) : (
                            <UserPlus className="h-4 w-4" />
                          )}
                          {translatorStatus === "connected"
                            ? "Translator Connected"
                            : translatorStatus === "connecting"
                              ? "Connecting Translator..."
                              : translatorRequested
                                ? "Translator Requested"
                                : "Request Human Translator"}
                        </Button>

                        {translatorStatus === "connected" && (
                          <div className="p-2 bg-green-50 border border-green-200 rounded text-xs">
                            <p className="font-medium text-green-800">Maria Rodriguez - Spanish Translator</p>
                            <p className="text-green-600">Available to assist with this call</p>
                          </div>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Call Controls with text labels */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Call Controls</h4>
                      <div className="space-y-2">
                        <Button
                          variant={isMuted ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => setIsMuted(!isMuted)}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                          {isMuted ? "Unmute Microphone" : "Mute Microphone"}
                        </Button>

                        <Button
                          variant={speakerOn ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSpeakerOn(!speakerOn)}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          {speakerOn ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                          {speakerOn ? "Turn Off Speaker" : "Turn On Speaker"}
                        </Button>

                        <Button
                          variant={isRecording ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => setIsRecording(!isRecording)}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          {isRecording ? <Square className="h-4 w-4" /> : <Record className="h-4 w-4" />}
                          {isRecording ? "Stop Recording" : "Start Recording"}
                        </Button>

                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => setIsCallActive(false)}
                          className="w-full flex items-center gap-2 justify-start"
                        >
                          <PhoneOff className="h-4 w-4" />
                          End Call
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="summary" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Emergency Summary Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bot className="h-5 w-5" />
                      Emergency Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">LOCATION</h4>
                          <p className="text-sm">{emergencySummary.location}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">EMERGENCY TYPE</h4>
                          <p className="text-sm">{emergencySummary.emergencyType}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">CALLER</h4>
                          <p className="text-sm">{emergencySummary.caller}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">TIME REPORTED</h4>
                          <p className="text-sm">{emergencySummary.timeReported}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground">STATUS</h4>
                          <Badge variant="default">{emergencySummary.status}</Badge>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">KEY DETAILS</h4>
                      <ul className="space-y-1">
                        {emergencySummary.keyDetails.map((detail, index) => (
                          <li key={index} className="text-sm flex items-start gap-2">
                            <span className="text-muted-foreground">•</span>
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                {/* Timeline Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Event Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96">
                      <div className="space-y-4">
                        {timelineEvents.map((event, index) => (
                          <div key={event.id} className="flex gap-3">
                            <div className="flex flex-col items-center">
                              <div
                                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                                  event.status === "completed"
                                    ? "bg-green-100"
                                    : event.status === "in_progress"
                                      ? "bg-blue-100"
                                      : "bg-gray-100"
                                }`}
                              >
                                {event.icon}
                              </div>
                              {index < timelineEvents.length - 1 && <div className="w-0.5 h-8 bg-gray-200 mt-2"></div>}
                            </div>
                            <div className="flex-1 pb-4">
                              <div className="flex items-center justify-between mb-1">
                                <h4 className="text-sm font-medium">{event.title}</h4>
                                <span className="text-xs text-muted-foreground">{event.time}</span>
                              </div>
                              <p className="text-xs text-muted-foreground">{event.description}</p>
                              <Badge
                                variant={event.status === "completed" ? "default" : "secondary"}
                                className="text-xs mt-1"
                              >
                                {event.status === "completed"
                                  ? "Completed"
                                  : event.status === "in_progress"
                                    ? "In Progress"
                                    : "Pending"}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>